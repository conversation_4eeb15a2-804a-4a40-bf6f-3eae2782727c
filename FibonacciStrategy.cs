//
// Copyright (C) 2025, NinjaTrader LLC <www.ninjatrader.com>.
// NinjaTrader reserves the right to modify or overwrite this NinjaScript component with each release.
//
#region Using declarations
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using System.Windows.Media;
using System.Xml.Serialization;
using NinjaTrader.Cbi;
using NinjaTrader.Gui;
using NinjaTrader.Gui.Chart;
using NinjaTrader.Gui.SuperDom;
using NinjaTrader.Gui.Tools;
using NinjaTrader.Data;
using NinjaTrader.NinjaScript;
using NinjaTrader.Core.FloatingPoint;
using NinjaTrader.NinjaScript.DrawingTools;
#endregion

//This namespace holds strategies in this folder and is required. Do not change it.
namespace NinjaTrader.NinjaScript.Strategies
{
	public class FibonacciStrategy : Strategy
	{
		// Fibonacci levels for drawing and signals
		private double[] fibLevels = { 1.1, 1.08, 1.0, 0.9, 0.1, 0.0, -0.08, -0.1 };
		private Brush[] fibColors;
		private readonly double greenLevel = 1.0;  // 100% level for buy signals
		private readonly double redLevel = 0.0;    // 0% level for sell signals

		// Multi-timeframe variables
		private DateTime lastMtfBarTime;
		private bool isNewMtfBar;
		private double mtfHigh, mtfLow, mtfOpen, mtfClose;
		private bool mtfDataInitialized;
		private int lastProcessedBar = -1;

		// Previous candle's Fibonacci levels for signal generation
		private double previousGreenLevel = double.NaN;
		private double previousRedLevel = double.NaN;

		// Signal tracking variables
		private bool greenSignalTriggered = false;
		private bool redSignalTriggered = false;

		// Position management
		private bool activeBuyPosition = false;
		private bool activeSellPosition = false;
		private double buyTpLevel = double.NaN;
		private double buySlLevel = double.NaN;
		private double sellTpLevel = double.NaN;
		private double sellSlLevel = double.NaN;
		
		protected override void OnStateChange()
		{
			if (State == State.SetDefaults)
			{
				Description					= @"Fibonacci strategy that buys at 1.0 level and sells at 0.0 level based on previous candle's Fibonacci levels";
				Name						= "FibonacciStrategy";
				Calculate					= Calculate.OnPriceChange;
				EntriesPerDirection			= 1;
				EntryHandling				= EntryHandling.AllEntries;
				IsExitOnSessionCloseStrategy = true;
				ExitOnSessionCloseSeconds	= 30;
				IsFillLimitOnTouch			= false;
				MaximumBarsLookBack			= MaximumBarsLookBack.TwoHundredFiftySix;
				OrderFillResolution			= OrderFillResolution.High;
				Slippage					= 0;
				StartBehavior				= StartBehavior.WaitUntilFlat;
				TimeInForce					= TimeInForce.Gtc;
				TraceOrders					= false;
				RealtimeErrorHandling		= RealtimeErrorHandling.StopCancelClose;
				StopTargetHandling			= StopTargetHandling.PerEntryExecution;
				BarsRequiredToTrade			= 20;
				IsInstantiatedOnEachOptimizationIteration = true;
				
				// Multi-timeframe parameters
				UseMtfMode					= false;
				MtfTimeframe				= 5;
				MtfPeriodType				= BarsPeriodType.Minute;
				UseConfirmedData			= true;

				// Signal parameters
				ShowSignals					= true;
				ShowImmediateSignals		= true;
				
				// Take profit and stop loss parameters
				TakeProfitMultiplier		= 1.1;  // TP at 1.1 level
				StopLossMultiplier			= -0.1; // SL at -0.1 level

				// Drawing parameters
				ShowLevels					= true;
				ShowLabels					= true;
				LineWidth					= 1;
				LineTransparency			= 20;

				// Color inputs
				FibColor11					= Brushes.Black;
				FibColor108					= Brushes.Black;
				FibColor10					= Brushes.Green;
				FibColor09					= Brushes.Orange;
				FibColor01					= Brushes.Purple;
				FibColor00					= Brushes.Red;
				FibColorNeg08				= Brushes.Black;
				FibColorNeg1				= Brushes.Black;
				TextColor					= Brushes.Black;
			}
			else if (State == State.Configure)
			{
				// Add multi-timeframe data series if MTF mode is enabled
				if (UseMtfMode)
				{
					try
					{
						AddDataSeries(MtfPeriodType, MtfTimeframe);
						Print($"Added MTF data series: {MtfTimeframe} {MtfPeriodType}");
					}
					catch (Exception ex)
					{
						Print($"Error adding MTF data series: {ex.Message}");
					}
				}
			}
			else if (State == State.DataLoaded)
			{
				// Initialize color array
				fibColors = new Brush[]
				{
					FibColor11, FibColor108, FibColor10, FibColor09,
					FibColor01, FibColor00, FibColorNeg08, FibColorNeg1
				};

				// Reset MTF state for chart switching
				ResetMtfState();
			}
			else if (State == State.Terminated)
			{
				// Clean up when strategy is removed
				ResetMtfState();
			}
		}

		protected override void OnBarUpdate()
		{
			if (CurrentBar < BarsRequiredToTrade)
				return;

			// Handle multi-timeframe mode
			if (UseMtfMode)
			{
				// Only process primary bars (chart timeframe) for strategy logic
				if (BarsInProgress != 0)
					return;

				// Check if we have enough bars on both timeframes
				if (CurrentBars[0] < 1 || (BarsArray.Length > 1 && CurrentBars[1] < 1))
					return;

				// Validate MTF data series exists
				if (BarsArray.Length < 2)
				{
					Print("Error: MTF data series not properly initialized");
					return;
				}

				// Reset state if we're processing a different bar than before (chart switching)
				if (lastProcessedBar != CurrentBar)
				{
					if (!mtfDataInitialized)
					{
						ResetMtfState();
						mtfDataInitialized = true;
					}
					lastProcessedBar = CurrentBar;
				}

				// Detect new MTF bar
				DateTime currentMtfTime = Times[1][0];
				isNewMtfBar = currentMtfTime != lastMtfBarTime;

				// Get MTF OHLC data based on confirmed data setting
				int mtfIndex = UseConfirmedData ? 1 : 0; // Use previous bar if confirmed data is enabled
				if (CurrentBars[1] >= mtfIndex)
				{
					try
					{
						mtfOpen = Opens[1][mtfIndex];
						mtfHigh = Highs[1][mtfIndex];
						mtfLow = Lows[1][mtfIndex];
						mtfClose = Closes[1][mtfIndex];

						// Validate MTF data
						if (mtfHigh <= 0 || mtfLow <= 0 || mtfHigh < mtfLow)
						{
							return; // Skip invalid data
						}
					}
					catch (Exception ex)
					{
						Print($"Error accessing MTF data: {ex.Message}");
						return;
					}
				}
				else
				{
					return; // Not enough MTF data yet
				}

				// Update lastMtfBarTime when we detect a new MTF bar
				if (isNewMtfBar)
				{
					lastMtfBarTime = currentMtfTime;
					
					// Store previous levels before calculating new ones
					if (!double.IsNaN(previousGreenLevel) && !double.IsNaN(previousRedLevel))
					{
						// Reset signal flags for new MTF period
						greenSignalTriggered = false;
						redSignalTriggered = false;
					}
					
					// Calculate new Fibonacci levels from MTF data
					double priceRange = mtfHigh - mtfLow;
					if (priceRange > 0)
					{
						previousGreenLevel = mtfLow + (priceRange * greenLevel); // 1.0 level
						previousRedLevel = mtfLow + (priceRange * redLevel);     // 0.0 level

						// Draw Fibonacci levels for MTF mode
						DrawFibonacciLevels(mtfLow, priceRange, true);
					}
				}

				// Process strategy signals using previous MTF levels
				ProcessStrategySignals();
			}
			else
			{
				// Per-bar mode: use previous bar's levels for signals
				if (CurrentBar >= 1)
				{
					// Store previous levels before calculating new ones
					if (CurrentBar > 1)
					{
						// Reset signal flags for new bar
						greenSignalTriggered = false;
						redSignalTriggered = false;
					}
					
					// Calculate Fibonacci levels from previous bar
					double prevHigh = High[1];
					double prevLow = Low[1];
					double priceRange = prevHigh - prevLow;
					
					if (priceRange > 0)
					{
						previousGreenLevel = prevLow + (priceRange * greenLevel); // 1.0 level
						previousRedLevel = prevLow + (priceRange * redLevel);     // 0.0 level

						// Draw Fibonacci levels for per-bar mode
						DrawFibonacciLevels(prevLow, priceRange, false);
					}

					// Process strategy signals using previous bar's levels
					ProcessStrategySignals();
				}
			}
		}

		private void ProcessStrategySignals()
		{
			if (!ShowSignals || double.IsNaN(previousGreenLevel) || double.IsNaN(previousRedLevel))
				return;

			// Check for take profit and stop loss conditions first
			CheckTakeProfitAndStopLoss();

			// Only process new signals if we don't have active positions or if immediate signals are enabled
			if (ShowImmediateSignals || (!activeBuyPosition && !activeSellPosition))
			{
				// Get current tick price for real-time execution
				double currentPrice = Close[0];
				double currentBid = GetCurrentBid();
				double currentAsk = GetCurrentAsk();

				// Use a tolerance for price matching to account for spread and minor price variations
				double tolerance = TickSize * 1.0;

				// For historical data, also check if the current bar's range includes the Fibonacci levels
				// This ensures we catch level touches in both tick replay and standard historical data
				bool buyCondition = false;
				bool sellCondition = false;

				// Check for buy signal: price touches previous candle's green line (1.0 level)
				if (State == State.Historical)
				{
					// In historical mode, check if the bar's range crossed the level
					buyCondition = (High[0] >= previousGreenLevel && Low[0] <= previousGreenLevel) && !greenSignalTriggered;
				}
				else
				{
					// In real-time mode, use current price with tolerance
					buyCondition = Math.Abs(currentPrice - previousGreenLevel) <= tolerance && !greenSignalTriggered;
				}

				// Check for sell signal: price touches previous candle's red line (0.0 level)
				if (State == State.Historical)
				{
					// In historical mode, check if the bar's range crossed the level
					sellCondition = (Low[0] <= previousRedLevel && High[0] >= previousRedLevel) && !redSignalTriggered;
				}
				else
				{
					// In real-time mode, use current price with tolerance
					sellCondition = Math.Abs(currentPrice - previousRedLevel) <= tolerance && !redSignalTriggered;
				}

				if (buyCondition)
				{
					ProcessBuySignal();
				}
				
				if (sellCondition)
				{
					ProcessSellSignal();
				}
			}
		}

		private void ProcessBuySignal()
		{
			if (activeBuyPosition)
				return;

			// Enter long position
			EnterLong("FibBuy");

			greenSignalTriggered = true;
			activeBuyPosition = true;
			activeSellPosition = false;

			// Calculate take profit and stop loss levels
			double priceRange = previousGreenLevel - previousRedLevel;
			buyTpLevel = previousRedLevel + (priceRange * TakeProfitMultiplier); // 1.1 level
			buySlLevel = previousRedLevel + (priceRange * StopLossMultiplier);   // -0.1 level

			// Set stop loss and take profit
			SetStopLoss("FibBuy", CalculationMode.Price, buySlLevel, false);
			SetProfitTarget("FibBuy", CalculationMode.Price, buyTpLevel);

			// Reset sell levels
			sellTpLevel = double.NaN;
			sellSlLevel = double.NaN;

			Print($"BUY signal at {previousGreenLevel:F2}, TP: {buyTpLevel:F2}, SL: {buySlLevel:F2}");
		}

		private void ProcessSellSignal()
		{
			if (activeSellPosition)
				return;

			// Enter short position
			EnterShort("FibSell");

			redSignalTriggered = true;
			activeSellPosition = true;
			activeBuyPosition = false;

			// Calculate take profit and stop loss levels
			double priceRange = previousGreenLevel - previousRedLevel;
			sellTpLevel = previousRedLevel + (priceRange * StopLossMultiplier);   // -0.1 level
			sellSlLevel = previousRedLevel + (priceRange * TakeProfitMultiplier); // 1.1 level (stop loss for short)

			// Set stop loss and take profit
			SetStopLoss("FibSell", CalculationMode.Price, sellSlLevel, false);
			SetProfitTarget("FibSell", CalculationMode.Price, sellTpLevel);

			// Reset buy levels
			buyTpLevel = double.NaN;
			buySlLevel = double.NaN;

			Print($"SELL signal at {previousRedLevel:F2}, TP: {sellTpLevel:F2}, SL: {sellSlLevel:F2}");
		}

		private void CheckTakeProfitAndStopLoss()
		{
			// Get current tick price for real-time TP/SL checking
			double currentPrice = Close[0];
			double tolerance = TickSize * 0.5;

			// Check buy position TP/SL
			if (activeBuyPosition && !double.IsNaN(buyTpLevel) && !double.IsNaN(buySlLevel))
			{
				if (currentPrice >= buyTpLevel)
				{
					Print($"Buy TP hit at {buyTpLevel:F2}, current price: {currentPrice:F2}");
					activeBuyPosition = false;
					buyTpLevel = double.NaN;
					buySlLevel = double.NaN;
				}
				else if (currentPrice <= buySlLevel)
				{
					Print($"Buy SL hit at {buySlLevel:F2}, current price: {currentPrice:F2}");
					activeBuyPosition = false;
					buyTpLevel = double.NaN;
					buySlLevel = double.NaN;
				}
			}

			// Check sell position TP/SL
			if (activeSellPosition && !double.IsNaN(sellTpLevel) && !double.IsNaN(sellSlLevel))
			{
				if (currentPrice <= sellTpLevel)
				{
					Print($"Sell TP hit at {sellTpLevel:F2}, current price: {currentPrice:F2}");
					activeSellPosition = false;
					sellTpLevel = double.NaN;
					sellSlLevel = double.NaN;
				}
				else if (currentPrice >= sellSlLevel)
				{
					Print($"Sell SL hit at {sellSlLevel:F2}, current price: {currentPrice:F2}");
					activeSellPosition = false;
					sellTpLevel = double.NaN;
					sellSlLevel = double.NaN;
				}
			}
		}

		private void ResetMtfState()
		{
			lastMtfBarTime = DateTime.MinValue;
			isNewMtfBar = false;
			mtfHigh = mtfLow = mtfOpen = mtfClose = 0;
			mtfDataInitialized = false;
			lastProcessedBar = -1;

			// Reset strategy state
			previousGreenLevel = double.NaN;
			previousRedLevel = double.NaN;
			greenSignalTriggered = false;
			redSignalTriggered = false;
			activeBuyPosition = false;
			activeSellPosition = false;
			buyTpLevel = double.NaN;
			buySlLevel = double.NaN;
			sellTpLevel = double.NaN;
			sellSlLevel = double.NaN;
		}

		protected override void OnOrderUpdate(Order order, double limitPrice, double stopPrice, int quantity, int filled, double averageFillPrice, OrderState orderState, DateTime time, ErrorCode error, string comment)
		{
			// Handle order updates for position tracking
			if (order.Name == "FibBuy" || order.Name == "FibSell")
			{
				if (orderState == OrderState.Filled)
				{
					Print($"Order filled: {order.Name} at {averageFillPrice:F2}");
				}
				else if (orderState == OrderState.Cancelled || orderState == OrderState.Rejected)
				{
					Print($"Order {orderState}: {order.Name}");
					// Reset position flags if entry order is cancelled/rejected
					if (order.OrderAction == OrderAction.Buy)
					{
						activeBuyPosition = false;
						buyTpLevel = double.NaN;
						buySlLevel = double.NaN;
					}
					else if (order.OrderAction == OrderAction.SellShort)
					{
						activeSellPosition = false;
						sellTpLevel = double.NaN;
						sellSlLevel = double.NaN;
					}
				}
			}
		}

		protected override void OnExecutionUpdate(Execution execution, string executionId, double price, int quantity, MarketPosition marketPosition, string orderId, DateTime time)
		{
			// Handle execution updates for position management
			if (execution.Order.Name == "FibBuy" || execution.Order.Name == "FibSell")
			{
				Print($"Execution: {execution.Order.Name} - {execution.Order.OrderAction} {quantity} at {price:F2}");

				// Update position status based on market position
				if (marketPosition == MarketPosition.Flat)
				{
					// Position closed
					activeBuyPosition = false;
					activeSellPosition = false;
					buyTpLevel = double.NaN;
					buySlLevel = double.NaN;
					sellTpLevel = double.NaN;
					sellSlLevel = double.NaN;
					Print("Position closed - flat");
				}
			}
		}

		private void DrawFibonacciLevels(double rangeLow, double priceRange, bool isMtfMode)
		{
			if (!ShowLevels)
				return;

			for (int i = 0; i < fibLevels.Length; i++)
			{
				double fibLevel = fibLevels[i];
				double fibPrice = rangeLow + (priceRange * fibLevel);

				// Create unique line names for MTF vs per-bar mode
				string lineName = isMtfMode ?
					$"MTFFibLine_{lastMtfBarTime.Ticks}_{i}" :
					$"FibLine_{CurrentBar}_{i}";

				// For MTF mode, draw lines spanning the MTF timeframe duration
				if (isMtfMode)
				{
					// Calculate the number of chart bars in the MTF timeframe
					int mtfDurationBars = CalculateMtfDurationInBars();

					// Calculate position offset based on confirmed data setting
					int positionOffset = UseConfirmedData ? mtfDurationBars : 0;

					// Calculate line start and end positions
					int lineStartBarsAgo = mtfDurationBars - 1 + positionOffset;
					int lineEndBarsAgo = positionOffset;

					// Draw line spanning the MTF period
					Draw.Line(this, lineName, lineStartBarsAgo, fibPrice, lineEndBarsAgo, fibPrice,
						GetColorWithTransparency(fibColors[i], LineTransparency));
				}
				else
				{
					// Original per-bar mode: line for current bar only
					Draw.Line(this, lineName, 0, fibPrice, 1, fibPrice,
						GetColorWithTransparency(fibColors[i], LineTransparency));
				}

				// Add label if enabled
				if (ShowLabels)
				{
					string labelName = isMtfMode ?
						$"MTFFibLabel_{lastMtfBarTime.Ticks}_{i}" :
						$"FibLabel_{CurrentBar}_{i}";
					string labelText = $"{fibLevel:F2} ({fibPrice:F3})";

					// Position label at the end of the line
					int labelPosition = isMtfMode ?
						(UseConfirmedData ? CalculateMtfDurationInBars() : 0) : 0;

					Draw.Text(this, labelName, labelText, labelPosition, fibPrice, TextColor);
				}
			}
		}

		private int CalculateMtfDurationInBars()
		{
			if (!UseMtfMode || BarsArray.Length < 2)
				return 1;

			try
			{
				// Calculate approximate number of chart bars in one MTF bar
				TimeSpan chartBarDuration = GetBarDuration(BarsArray[0].BarsPeriod);
				TimeSpan mtfBarDuration = GetBarDuration(BarsArray[1].BarsPeriod);

				if (chartBarDuration.TotalSeconds > 0 && mtfBarDuration.TotalSeconds > 0)
				{
					double ratio = mtfBarDuration.TotalSeconds / chartBarDuration.TotalSeconds;
					int barsInMtf = (int)Math.Round(ratio);

					// Ensure reasonable bounds
					return Math.Max(1, Math.Min(barsInMtf, 1000));
				}
			}
			catch (Exception ex)
			{
				Print($"Error calculating MTF duration: {ex.Message}");
			}

			return 1;
		}

		private TimeSpan GetBarDuration(BarsPeriod barsPeriod)
		{
			switch (barsPeriod.BarsPeriodType)
			{
				case BarsPeriodType.Second:
					return TimeSpan.FromSeconds(barsPeriod.Value);
				case BarsPeriodType.Minute:
					return TimeSpan.FromMinutes(barsPeriod.Value);
				case BarsPeriodType.Day:
					return TimeSpan.FromDays(barsPeriod.Value);
				case BarsPeriodType.Week:
					return TimeSpan.FromDays(barsPeriod.Value * 7);
				case BarsPeriodType.Month:
					return TimeSpan.FromDays(barsPeriod.Value * 30); // Approximate
				default:
					return TimeSpan.FromMinutes(1); // Default fallback
			}
		}

		private Brush GetColorWithTransparency(Brush originalBrush, int transparency)
		{
			if (originalBrush is SolidColorBrush solidBrush)
			{
				Color color = solidBrush.Color;
				byte alpha = (byte)(255 * (100 - transparency) / 100);
				return new SolidColorBrush(Color.FromArgb(alpha, color.R, color.G, color.B));
			}
			return originalBrush;
		}

		#region Properties
		[NinjaScriptProperty]
		[Display(Name="Use Multi-Timeframe Mode", Description="Enable multi-timeframe Fibonacci strategy", Order=1, GroupName="Multi-Timeframe Settings")]
		public bool UseMtfMode
		{ get; set; }

		[NinjaScriptProperty]
		[Range(1, int.MaxValue)]
		[Display(Name="MTF Timeframe Value", Description="Multi-timeframe period value", Order=2, GroupName="Multi-Timeframe Settings")]
		public int MtfTimeframe
		{ get; set; }

		[NinjaScriptProperty]
		[Display(Name="MTF Period Type", Description="Multi-timeframe period type", Order=3, GroupName="Multi-Timeframe Settings")]
		public BarsPeriodType MtfPeriodType
		{ get; set; }

		[NinjaScriptProperty]
		[Display(Name="Use Confirmed Data", Description="Use confirmed MTF data (no repaint)", Order=4, GroupName="Multi-Timeframe Settings")]
		public bool UseConfirmedData
		{ get; set; }

		[NinjaScriptProperty]
		[Display(Name="Show Signals", Description="Enable signal generation", Order=1, GroupName="Signal Settings")]
		public bool ShowSignals
		{ get; set; }

		[NinjaScriptProperty]
		[Display(Name="Show Immediate Signals", Description="Allow signals even with active positions", Order=2, GroupName="Signal Settings")]
		public bool ShowImmediateSignals
		{ get; set; }

		[NinjaScriptProperty]
		[Range(0.1, 5.0)]
		[Display(Name="Take Profit Multiplier", Description="Take profit level multiplier (1.1 = 110% level)", Order=1, GroupName="Risk Management")]
		public double TakeProfitMultiplier
		{ get; set; }

		[NinjaScriptProperty]
		[Range(-1.0, 0.5)]
		[Display(Name="Stop Loss Multiplier", Description="Stop loss level multiplier (-0.1 = -10% level)", Order=2, GroupName="Risk Management")]
		public double StopLossMultiplier
		{ get; set; }

		[NinjaScriptProperty]
		[Display(Name="Show Levels", Description="Show Fibonacci Levels", Order=1, GroupName="Display Settings")]
		public bool ShowLevels
		{ get; set; }

		[NinjaScriptProperty]
		[Display(Name="Show Labels", Description="Show Level Labels", Order=2, GroupName="Display Settings")]
		public bool ShowLabels
		{ get; set; }

		[NinjaScriptProperty]
		[Range(1, 5)]
		[Display(Name="Line Width", Description="Line Width", Order=3, GroupName="Display Settings")]
		public int LineWidth
		{ get; set; }

		[NinjaScriptProperty]
		[Range(0, 100)]
		[Display(Name="Line Transparency", Description="Line Transparency", Order=4, GroupName="Display Settings")]
		public int LineTransparency
		{ get; set; }

		[NinjaScriptProperty]
		[XmlIgnore]
		[Display(Name="1.1 Level Color", Description="1.1 Level Color", Order=1, GroupName="Colors")]
		public Brush FibColor11
		{ get; set; }

		[Browsable(false)]
		public string FibColor11Serializable
		{
			get { return Serialize.BrushToString(FibColor11); }
			set { FibColor11 = Serialize.StringToBrush(value); }
		}

		[NinjaScriptProperty]
		[XmlIgnore]
		[Display(Name="1.08 Level Color", Description="1.08 Level Color", Order=2, GroupName="Colors")]
		public Brush FibColor108
		{ get; set; }

		[Browsable(false)]
		public string FibColor108Serializable
		{
			get { return Serialize.BrushToString(FibColor108); }
			set { FibColor108 = Serialize.StringToBrush(value); }
		}

		[NinjaScriptProperty]
		[XmlIgnore]
		[Display(Name="1.0 Level Color", Description="1.0 Level Color", Order=3, GroupName="Colors")]
		public Brush FibColor10
		{ get; set; }

		[Browsable(false)]
		public string FibColor10Serializable
		{
			get { return Serialize.BrushToString(FibColor10); }
			set { FibColor10 = Serialize.StringToBrush(value); }
		}

		[NinjaScriptProperty]
		[XmlIgnore]
		[Display(Name="0.9 Level Color", Description="0.9 Level Color", Order=4, GroupName="Colors")]
		public Brush FibColor09
		{ get; set; }

		[Browsable(false)]
		public string FibColor09Serializable
		{
			get { return Serialize.BrushToString(FibColor09); }
			set { FibColor09 = Serialize.StringToBrush(value); }
		}

		[NinjaScriptProperty]
		[XmlIgnore]
		[Display(Name="0.1 Level Color", Description="0.1 Level Color", Order=5, GroupName="Colors")]
		public Brush FibColor01
		{ get; set; }

		[Browsable(false)]
		public string FibColor01Serializable
		{
			get { return Serialize.BrushToString(FibColor01); }
			set { FibColor01 = Serialize.StringToBrush(value); }
		}

		[NinjaScriptProperty]
		[XmlIgnore]
		[Display(Name="0.0 Level Color", Description="0.0 Level Color", Order=6, GroupName="Colors")]
		public Brush FibColor00
		{ get; set; }

		[Browsable(false)]
		public string FibColor00Serializable
		{
			get { return Serialize.BrushToString(FibColor00); }
			set { FibColor00 = Serialize.StringToBrush(value); }
		}

		[NinjaScriptProperty]
		[XmlIgnore]
		[Display(Name="-0.08 Level Color", Description="-0.08 Level Color", Order=7, GroupName="Colors")]
		public Brush FibColorNeg08
		{ get; set; }

		[Browsable(false)]
		public string FibColorNeg08Serializable
		{
			get { return Serialize.BrushToString(FibColorNeg08); }
			set { FibColorNeg08 = Serialize.StringToBrush(value); }
		}

		[NinjaScriptProperty]
		[XmlIgnore]
		[Display(Name="-0.1 Level Color", Description="-0.1 Level Color", Order=8, GroupName="Colors")]
		public Brush FibColorNeg1
		{ get; set; }

		[Browsable(false)]
		public string FibColorNeg1Serializable
		{
			get { return Serialize.BrushToString(FibColorNeg1); }
			set { FibColorNeg1 = Serialize.StringToBrush(value); }
		}

		[NinjaScriptProperty]
		[XmlIgnore]
		[Display(Name="Label Text Color", Description="Label Text Color", Order=9, GroupName="Colors")]
		public Brush TextColor
		{ get; set; }

		[Browsable(false)]
		public string TextColorSerializable
		{
			get { return Serialize.BrushToString(TextColor); }
			set { TextColor = Serialize.StringToBrush(value); }
		}
		#endregion
	}
}